import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'habit.dart';
import 'section.dart';
import 'database_service.dart';
import 'habit_table_view.dart';
import 'modern_theme.dart';
import 'enhanced_habit_table_view.dart';

class HabitsInSectionScreen extends StatefulWidget {
  final Section section;

  const HabitsInSectionScreen({
    super.key,
    required this.section,
  });

  @override
  State<HabitsInSectionScreen> createState() => _HabitsInSectionScreenState();
}

class _HabitsInSectionScreenState extends State<HabitsInSectionScreen> {
  final _databaseService = DatabaseService();
  
  late Future<void> _dataFuture;
  List<Habit> _allHabits = [];
  List<Section> _allSections = [];
  List<Habit> _sectionHabits = [];

  @override
  void initState() {
    super.initState();
    _dataFuture = _loadData();
  }

  Future<void> _loadData() async {
    try {
      final results = await Future.wait([
        _databaseService.loadAllHabits(),
        _databaseService.loadAllSections(),
      ]);

      _allHabits = results[0] as List<Habit>;
      _allSections = results[1] as List<Section>;

      _filterHabitsForSection();
    } catch (e, stackTrace) {
      debugPrint('ERROR: Failed to load data - $e');
      debugPrint('StackTrace: $stackTrace');
      rethrow;
    }
  }

  void _filterHabitsForSection() {
    // Get habits that belong to this section
    final habitsInSection = _allHabits
        .where((habit) => habit.sectionIds.contains(widget.section.id))
        .toList();

    // CRITICAL FIX: Get the current section from database to ensure we have the latest habitOrder
    final currentSection = _allSections.firstWhere(
      (section) => section.id == widget.section.id,
      orElse: () => widget.section, // Fallback to original if not found
    );

    // Sort habits according to the current section's habitOrder (from database)
    _sectionHabits = _sortHabitsByOrder(habitsInSection, currentSection.habitOrder);
  }

  // CRITICAL FIX: Add dedicated reload method for real-time UI updates
  Future<void> _reloadData() async {
    setState(() {
      _dataFuture = _loadData();
    });
  }

  List<Habit> _sortHabitsByOrder(List<Habit> habits, List<String> habitOrder) {
    if (habitOrder.isEmpty) {
      return habits; // Return as-is if no order specified
    }

    final sortedHabits = <Habit>[];
    
    // First, add habits in the specified order
    for (final habitId in habitOrder) {
      final habit = habits.firstWhere(
        (h) => h.id == habitId,
        orElse: () => Habit(name: ''), // Dummy habit that will be filtered out
      );
      if (habit.name.isNotEmpty) {
        sortedHabits.add(habit);
      }
    }
    
    // Then add any habits not in the order list
    for (final habit in habits) {
      if (!habitOrder.contains(habit.id)) {
        sortedHabits.add(habit);
      }
    }
    
    return sortedHabits;
  }

  Future<void> _onReorder(int oldIndex, int newIndex) async {
    try {
      debugPrint('[SECTION_REORDER] Reordering habit from $oldIndex to $newIndex');
      
      setState(() {
        // Adjust newIndex if moving item down the list
        if (oldIndex < newIndex) {
          newIndex -= 1;
        }
        
        // Update local list
        final habit = _sectionHabits.removeAt(oldIndex);
        _sectionHabits.insert(newIndex, habit);
      });

      // Update the section's habitOrder list
      final newHabitOrder = _sectionHabits.map((habit) => habit.id).toList();
      
      // CRITICAL FIX: Get the current section from database to ensure we have the latest data
      final currentSection = _allSections.firstWhere(
        (section) => section.id == widget.section.id,
        orElse: () => widget.section, // Fallback to original if not found
      );
      
      final updatedSection = currentSection.copyWith(
        habitOrder: newHabitOrder,
      );

      // Save the updated section to the database
      await _databaseService.updateSection(updatedSection);
      
      // THIS IS THE CRITICAL FIX: Reload all data to reflect the new order
      await _reloadData();
      
      debugPrint('[SECTION_REORDER] Successfully updated section habit order');
      
    } catch (e, stackTrace) {
      debugPrint('[ERROR] Failed to reorder habits in section: $e');
      debugPrint('[ERROR] StackTrace: $stackTrace');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reorder habits: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      
      // Reload data to revert changes
      setState(() {
        _dataFuture = _loadData();
      });
    }
  }

  List<DateTime> _generateDates() {
    final now = DateTime.now();
    final List<DateTime> dateList = [];
    for (int i = 6; i >= 0; i--) { // Show 7 days for compact view
      dateList.add(DateTime(now.year, now.month, now.day - i));
    }
    return dateList.reversed.toList();
  }

  // Calculate completion percentage for the section
  double _calculateCompletionPercentage() {
    debugPrint('[DEBUG] _calculateCompletionPercentage: Starting calculation');
    debugPrint('[DEBUG] Section habits count: ${_sectionHabits.length}');
    
    if (_sectionHabits.isEmpty) {
      debugPrint('[DEBUG] No habits in section, returning 0.0');
      return 0.0;
    }
    
    final today = DateTime.now();
    final todayDateTime = DateTime(today.year, today.month, today.day);
    debugPrint('[DEBUG] Today DateTime: $todayDateTime');
    
    int completedCount = 0;
    int totalCount = _sectionHabits.length;
    
    for (final habit in _sectionHabits) {
      debugPrint('[DEBUG] Checking habit: ${habit.name}');
      debugPrint('[DEBUG] Habit completions: ${habit.completions}');
      
      // Fixed: Use the correct method to check completion
      final isCompleted = habit.isCompletedOnDate(todayDateTime);
      debugPrint('[DEBUG] Habit ${habit.name} completed today: $isCompleted');
      
      if (isCompleted) {
        completedCount++;
      }
    }
    
    final percentage = totalCount > 0 ? (completedCount / totalCount) * 100 : 0.0;
    debugPrint('[DEBUG] Completion calculation: $completedCount/$totalCount = $percentage%');
    
    return percentage;
  }

  Color _getSectionColor() {
    try {
      return Color(int.parse(widget.section.color.replaceFirst('#', '0xFF')));
    } catch (e) {
      final isDark = Theme.of(context).brightness == Brightness.dark;
      return isDark ? ModernTheme.darkAccent : ModernTheme.lightAccent;
    }
  }

  Widget _buildRefinedHeader() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final sectionColor = _getSectionColor();
    final completionPercentage = _calculateCompletionPercentage();
    
    return Row(
      children: [
        // Section name
        Text(
          widget.section.name,
          style: GoogleFonts.inter(
            fontSize: 18, // 10% reduction from 20
            fontWeight: FontWeight.w600,
            color: isDark ? ModernTheme.darkTextPrimary : ModernTheme.lightTextPrimary,
          ),
        ),
        
        SizedBox(width: ModernTheme.spaceSM),
        
        // Completion percentage with up arrow
        Row(
          children: [
            Icon(
              Icons.keyboard_arrow_up,
              size: 16,
              color: sectionColor,
            ),
            Text(
              '${completionPercentage.round()}%',
              style: GoogleFonts.inter(
                fontSize: 14.4, // 10% reduction from 16
                fontWeight: FontWeight.w500,
                color: sectionColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        title: _buildRefinedHeader(),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
          color: Theme.of(context).appBarTheme.iconTheme?.color,
        ),
      ),
      body: SafeArea(
        child: FutureBuilder<void>(
          future: _dataFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text('Error loading data: ${snapshot.error}'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _dataFuture = _loadData();
                        });
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            } else if (_sectionHabits.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.library_add_check_outlined,
                      size: 60,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No habits in "${widget.section.name}"',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add habits to this section to see them here',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            // Build the table view with section habits
            final dates = _generateDates();
            
            return Padding(
              padding: EdgeInsets.all(ModernTheme.spaceMD), // 25% reduction: 12px from 16px
              child: EnhancedHabitTableView(
                habits: _sectionHabits,
                dates: dates,
                sections: _allSections,
                onReorder: _onReorder,
                showReorderDialog: true,
                showPercentageRow: false, // Remove percentage row for cleaner section view
                onDataChanged: _reloadData, // THIS IS THE CRITICAL FIX: Reload data after habit completion toggles
              ),
            );
          },
        ),
      ),
    );
  }
}